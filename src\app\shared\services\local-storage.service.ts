import { Injectable } from '@angular/core';

export const STORAGE_KEY = {
    TOKEN: 'auth-token'
};

@Injectable({
    providedIn: 'root'
})
export class LocalStorageService {
    private secretKey = 'v5YU9ay9oWS1tNgHaG7cF51IO0ZcRxHu';
    constructor() {}

    encryptData(data: string): string {
        return AES.encrypt(data, this.secretKey).toString();
    }

    decryptData(encryptedData: string): string {
        try {
            const bytes = AES.decrypt(encryptedData, this.secretKey);
            return bytes.toString(enc.Utf8);
        } catch (error) {
            console.error('decryptData error:', error);
            return '';
        }
    }

    public saveData(key: string, value: string) {
        const encryptedToken = this.encryptData(value);
        localStorage.setItem(key, encryptedToken);
    }

    public getData(key: string) {
        const value: string = localStorage.getItem(key) || '';
        return this.decryptData(value);
    }

    public removeData(key: string) {
        localStorage.removeItem(key);
    }

    public clearData() {
        localStorage.clear();
    }
}
